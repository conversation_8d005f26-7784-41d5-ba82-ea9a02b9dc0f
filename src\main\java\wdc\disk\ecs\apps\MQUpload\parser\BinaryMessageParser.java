package wdc.disk.ecs.apps.MQUpload.parser;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
* Binary Message Parser Implementation
*/
public class BinaryMessageParser implements MessageParser {
   
   @Override
   public ParseResult parse(String message) {
       // For binary messages, we expect the input to be a hex string
       if (message == null || message.isEmpty()) {
           return new ParseResult(null, null);
       }

       byte[] binaryData = hexStringToByteArray(message);
       ByteBuffer buffer = ByteBuffer.wrap(binaryData);
       
       // Set to LITTLE_ENDIAN to match the original C code's behavior
       buffer.order(ByteOrder.LITTLE_ENDIAN);

       try {
           // Parse header (12 bytes: 4 for MsgId, 4 for CorrelId, 4 for Length)
           int messageId = buffer.getInt();
           int correlationId = buffer.getInt();
           int messageLength = buffer.getInt();

           // Parse remaining data based on messageLength
           String[] parsedFields = new String[3];
           parsedFields[0] = String.valueOf(messageId);
           parsedFields[1] = String.valueOf(correlationId);
           parsedFields[2] = String.valueOf(messageLength);

           return new ParseResult(parsedFields, null);
       } catch (Exception e) {
           return new ParseResult(null, null);
       }
   }

   private byte[] hexStringToByteArray(String hex) {
       int len = hex.length();
       byte[] data = new byte[len / 2];
       for (int i = 0; i < len; i += 2) {
           data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                   + Character.digit(hex.charAt(i + 1), 16));
       }
       return data;
   }
}