package wdc.disk.ecs.apps.MQUpload.producer;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;

import wdc.disk.ecs.apps.MQUpload.processor.AsciiFileProcessor;
import wdc.disk.ecs.apps.MQUpload.processor.BinaryFileProcessor;
import wdc.disk.ecs.apps.MQUpload.processor.FileProcessingTemplate;
import wdc.disk.ecs.apps.MQUpload.processor.FileProcessor;
import wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader;
import wdc.disk.ecs.apps.MQUpload.service.FileOperationService;
import wdc.disk.ecs.apps.MQUpload.service.RabbitMQProducerService;

/**
 * Main class for the MQUpload application that monitors directories and uploads files to RabbitMQ.
 * This class will do the file monitoring, processing, and uploading operations.
 *
 */
public class MQUpload implements Runnable {
    private final LogStream log;
    private final String configProfile;
    private ConfigurationLoader configLoader;  
    private FileOperationService fileMonitorService;
    private RabbitMQProducerService rabbitMQService;
    private boolean isRunning;
    private FileProcessingTemplate fileProcessingTemplate;
    private ScheduledExecutorService scheduler;

    public MQUpload(String configProfile, LogStream log) {
        this.configProfile = configProfile;
        this.log = log;
        this.isRunning = false;
    }

    public void initialize() throws Exception {
        // Load configuration
        configLoader = new ConfigurationLoader(log, configProfile);
        configLoader.loadConfiguration();

        // Initialize services using producer properties
        fileMonitorService = new FileOperationService(
            configLoader.getProducerProperties().getPdsDir(), 
            log, 
            configLoader.getProducerProperties().getWaitingForWriteEnded(),
            configLoader.getProducerProperties().getMaxFilesInSending()
        );
        
        rabbitMQService = new RabbitMQProducerService(configLoader.getRabbitMQProperties(), log);

        // Initialize profiles first
        DiskProfile dbMsgIDProfile = null;
        if (!configLoader.getProducerProperties().isBinaryFormat()) {  
            try {
                dbMsgIDProfile = new DiskProfile(
                    configLoader.getProducerProperties().getDbMsgIdProfile(),
                    "wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord",
                    DiskProfile.KEEP_ONE,
                    log
                );
                log.write(LogStream.INFORMATION_MESSAGE, 
                    "Initialized DB Message ID Profile: " + configLoader.getProducerProperties().getDbMsgIdProfile());
            } catch (ProfileException e) {
                log.write(LogStream.ERROR_MESSAGE, 
                    "Failed to initialize DB Message ID Profile: " + e.getMessage());
                throw e;
            }
        }

        // Initialize processor based on configuration
        FileProcessor processor = configLoader.getProducerProperties().isBinaryFormat()
            ? new BinaryFileProcessor(log, dbMsgIDProfile, configLoader.getProducerProperties().getDbMsgIdProfile())
            : new AsciiFileProcessor(log, dbMsgIDProfile, configLoader.getProducerProperties().getDbMsgIdProfile());

        // Initialize template
        fileProcessingTemplate = new FileProcessingTemplate(log, processor, rabbitMQService, fileMonitorService);

        // Create scheduler
        scheduler = Executors.newScheduledThreadPool(1);
    }

    public void start() {
        if (isRunning) {
            log.write(LogStream.WARNING_MESSAGE, "MQUpload is already running");
            return;
        }

        isRunning = true;
        scheduler.scheduleAtFixedRate(this, 0, 
            configLoader.getProducerProperties().getMonitorInterval(), TimeUnit.SECONDS);
        log.write(LogStream.INFORMATION_MESSAGE, 
            "MQUpload started. Monitoring directory: " + configLoader.getProducerProperties().getPdsDir());
    }

    /**
     * Stops the file monitoring and upload process.
     */
    public void stop() {
        if (!isRunning) {
            return;
        }

        isRunning = false;
        scheduler.shutdown();
        try {
            scheduler.awaitTermination(60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.write(LogStream.ERROR_MESSAGE, "Error while stopping MQUpload: " + e.getMessage());
        }

        rabbitMQService.close();
        log.write(LogStream.INFORMATION_MESSAGE, "MQUpload stopped");
    }

    @Override
    public void run() {
        if (!isRunning) {
            return;
        }

        try {
            fileProcessingTemplate.processAndUploadFiles();
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Error in run cycle: " + e.getMessage());
        }
    }

    /**
     * Gets the current configuration properties.
     *
     * @return The current ConfigurationProperties instance
     */
    // Remove getConfig() and setConfig() methods as they're no longer needed
    
    // Add getter for ConfigurationLoader if needed
    public ConfigurationLoader getConfigLoader() {
        return configLoader;
    }
    
    /**
     * Checks if the application is currently running.
     *
     * @return true if the application is running, false otherwise
     */
    public boolean isRunning() {
        return isRunning;
    }
    
    public static void main(String[] args) {
        if (args.length != 2) {
            System.err.println("Usage: MQUpload <configProfile> <LOG_FILE>");
            System.exit(1);
        }

        String configProfile = args[0];
        String logfilePath = args[1];
        LogStream log = new LogStream(logfilePath);

        try {
            MQUpload mqUpload = new MQUpload(configProfile, log);
            
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.write(LogStream.INFORMATION_MESSAGE, "Shutting down MQUpload...");
                mqUpload.stop();
            }));

            mqUpload.initialize();
            mqUpload.start();

            // Keep the main thread alive
            while (mqUpload.isRunning()) {
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Application error: " + e.getMessage());
            System.exit(1);
        }
    }
}