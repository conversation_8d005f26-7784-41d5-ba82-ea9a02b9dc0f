package wdc.disk.ecs.apps.MQUpload.model;

public class ProducerProperties {
    private String pdsDir;
    private boolean binaryFormat;
    private int maxFilesInSending;
    private long waitingForWriteEnded;
    private boolean packSameMsgId;
    private String toDir;
    private String dbMsgIdProfile;
    private String queueConfigPath;
	private int monitorInterval = 60; // Default 60 seconds
	
	public String getPdsDir() {
		return pdsDir;
	}
	public void setPdsDir(String pdsDir) {
		this.pdsDir = pdsDir;
	}
	public boolean isBinaryFormat() {
		return binaryFormat;
	}
	public void setBinaryFormat(boolean binaryFormat) {
		this.binaryFormat = binaryFormat;
	}
	public int getMaxFilesInSending() {
		return maxFilesInSending;
	}
	public void setMaxFilesInSending(int maxFilesInSending) {
		this.maxFilesInSending = maxFilesInSending;
	}
	public long getWaitingForWriteEnded() {
		return waitingForWriteEnded;
	}
	public void setWaitingForWriteEnded(long waitingForWriteEnded) {
		this.waitingForWriteEnded = waitingForWriteEnded;
	}
	public boolean isPackSameMsgId() {
		return packSameMsgId;
	}
	public void setPackSameMsgId(boolean packSameMsgId) {
		this.packSameMsgId = packSameMsgId;
	}
	public String getToDir() {
		return toDir;
	}
	public void setToDir(String toDir) {
		this.toDir = toDir;
	}
	public String getDbMsgIdProfile() {
		return dbMsgIdProfile;
	}
	public void setDbMsgIdProfile(String dbMsgIdProfile) {
		this.dbMsgIdProfile = dbMsgIdProfile;
	}
	public String getQueueConfigPath() {
		return queueConfigPath;
	}
	public void setQueueConfigPath(String queueConfigPath) {
		this.queueConfigPath = queueConfigPath;
	}
    
    public int getMonitorInterval() {
        return monitorInterval;
    }

    public void setMonitorInterval(int monitorInterval) {
        this.monitorInterval = monitorInterval;
    }
}