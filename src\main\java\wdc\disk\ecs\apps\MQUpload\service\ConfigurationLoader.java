package wdc.disk.ecs.apps.MQUpload.service;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.ProducerProperties;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import org.yaml.snakeyaml.Yaml;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.ArrayList;
import java.util.List;

public class ConfigurationLoader {
    private final LogStream log;
    private final String configPath;
    private final ProducerProperties producerProperties;
    private final RabbitMQProperties rabbitMQProperties;
    private final ConsumerProperties consumerProperties;

    public ConfigurationLoader(LogStream log, String configPath) {
        this.log = log;
        this.configPath = configPath;
        this.producerProperties = new ProducerProperties();
        this.rabbitMQProperties = new RabbitMQProperties();
        this.consumerProperties = new ConsumerProperties();
    }

    public void loadConfiguration() throws SFTPSendException {
        File configFile = new File(configPath);
        if (!configFile.exists() || !configFile.isFile()) {
            throw new SFTPSendException("Configuration file does not exist or is not a file: " + configPath);
        }else{
            log.write(LogStream.INFORMATION_MESSAGE, "Configuration file found: " + configPath);
        }
        try (InputStream input = new FileInputStream(new File(configPath))) {
            Yaml yaml;
            Map<String, Object> config;
            try {
                yaml = new Yaml();
                config = yaml.load(input);
            } catch (Exception e) {
                log.writeExceptionStack(e);
                throw new SFTPSendException("Could not read YAML configuration file: " + configPath);
            }
            
            // Load and validate Producer configurations
            if (config.containsKey("producer")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> producerMap = (Map<String, Object>) config.get("producer");
                loadProducerConfig(producerMap);
                validateProducerConfig();
            }
            
            // Load and validate RabbitMQ configurations
            if (config.containsKey("rabbitmq")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> rabbitMap = (Map<String, Object>) config.get("rabbitmq");
                loadRabbitMQConfig(rabbitMap);
                validateRabbitMQConfig();
            }
            
            // Load and validate Consumer configurations
            if (config.containsKey("consumer")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> consumerMap = (Map<String, Object>) config.get("consumer");
                loadConsumerConfig(consumerMap);
                validateConsumerConfig();
            }
        } catch (java.io.IOException e) {
            log.writeExceptionStack(e);
            throw new SFTPSendException("Could not read configuration file: " + configPath);
        }
    }

    private void validateProducerConfig() throws SFTPSendException {
        if (producerProperties.getPdsDir() == null || producerProperties.getPdsDir().isEmpty()) {
            throw new SFTPSendException("PDS_DIR is required");
        }
        if (producerProperties.getMaxFilesInSending() <= 0) {
            throw new SFTPSendException("MAX_FILES_IN_SENDING must be greater than 0");
        }
        if (producerProperties.getWaitingForWriteEnded() < 0) {
            throw new SFTPSendException("WAITING_FOR_WRITE_ENDED must not be negative");
        }
        if (producerProperties.getToDir() == null || producerProperties.getToDir().isEmpty()) {
            throw new SFTPSendException("TO_DIR is required");
        }
        if (producerProperties.getDbMsgIdProfile() == null || producerProperties.getDbMsgIdProfile().isEmpty()) {
            throw new SFTPSendException("DB_MSG_ID_PROFILE is required");
        }


        // Validate directory existence
        validateDirectoryExists(producerProperties.getPdsDir(), "PDS_DIR");
        validateDirectoryExists(producerProperties.getToDir(), "TO_DIR");
    }

    private void validateRabbitMQConfig() throws SFTPSendException {
        if (rabbitMQProperties.getRabbitMQHosts() == null || rabbitMQProperties.getRabbitMQHosts().length == 0) {
            throw new SFTPSendException("RABBITMQ_HOSTS is required");
        }
        if (rabbitMQProperties.getRabbitMQPort() <= 0 || rabbitMQProperties.getRabbitMQPort() > 65535) {
            throw new SFTPSendException("RABBITMQ_PORT must be between 1 and 65535");
        }
        if (rabbitMQProperties.getRabbitMQUsername() == null || rabbitMQProperties.getRabbitMQUsername().isEmpty()) {
            throw new SFTPSendException("RABBITMQ_USERNAME is required");
        }
        if (rabbitMQProperties.getRabbitMQPassword() == null || rabbitMQProperties.getRabbitMQPassword().isEmpty()) {
            throw new SFTPSendException("RABBITMQ_PASSWORD is required");
        }
    }

    private void validateConsumerConfig() throws SFTPSendException {
        // Validate mybatis config path
        String mybatisConfigPath = consumerProperties.getMybatisConfigPath();
        if (mybatisConfigPath == null || mybatisConfigPath.isEmpty()) {
            throw new SFTPSendException("MYBATIS_CONFIG_PATH is required in consumer configuration");
        }

        // Validate that the mybatis config file exists
        File mybatisConfig = new File(mybatisConfigPath);
        if (!mybatisConfig.exists() || !mybatisConfig.isFile()) {
            throw new SFTPSendException("MyBatis configuration file not found: " + mybatisConfigPath);
        }

        // Add any additional consumer configuration validation here
        // For example, validate queue configurations if needed
    }

    // Remove the original validateConfiguration method as it's now split into separate methods
    private void loadProducerConfig(Map<String, Object> producerConfig) {
        if (producerConfig == null) {
            throw new IllegalArgumentException("Producer configuration cannot be null");
        }

        String pdsDir = getString(producerConfig, "pds_dir");
        String toDir = getString(producerConfig, "to_dir");
        String dbMsgIdProfile = getString(producerConfig, "db_msg_id_profile");
        
        // Set required string properties
        producerProperties.setPdsDir(pdsDir != null ? normalizeAndEnsureEndingPathSeparator(pdsDir) : null);
        producerProperties.setToDir(toDir != null ? normalizeAndEnsureEndingPathSeparator(toDir) : null);
        producerProperties.setDbMsgIdProfile(dbMsgIdProfile);

        // Set numeric and boolean properties with defaults
        producerProperties.setBinaryFormat(getBoolean(producerConfig, "binary_format"));
        producerProperties.setMaxFilesInSending(getInteger(producerConfig, "max_files_in_sending"));
        producerProperties.setWaitingForWriteEnded(getLong(producerConfig, "waiting_for_write_ended"));
        producerProperties.setMonitorInterval(getInteger(producerConfig, "monitor_interval"));
    }

    private void loadRabbitMQConfig(Map<String, Object> rabbitConfig) {
        // Get hosts as comma-separated string and split into array
        String hostsStr = getString(rabbitConfig, "host");
        String[] hosts = hostsStr.split(",");
        for (int i = 0; i < hosts.length; i++) {
            hosts[i] = hosts[i].trim(); // Remove any whitespace
        }
        
        rabbitMQProperties.setRabbitMQHosts(hosts);
        rabbitMQProperties.setRabbitMQPort(getInteger(rabbitConfig, "port"));
        rabbitMQProperties.setRabbitMQUsername(getString(rabbitConfig, "username"));
        rabbitMQProperties.setRabbitMQPassword(getString(rabbitConfig, "password"));
        rabbitMQProperties.setRabbitMQExchange(getString(rabbitConfig, "exchange"));
        rabbitMQProperties.setRabbitMQQueueConfigPath(getString(rabbitConfig, "queue_config_path"));
    }

    private void loadConsumerConfig(Map<String, Object> consumerConfig) {
        if (consumerConfig == null) {
            throw new IllegalArgumentException("Consumer configuration cannot be null");
        }

        // Load mybatis config path
        String mybatisConfigPath = getString(consumerConfig, "mybatis_config_path");
        consumerProperties.setMybatisConfigPath(mybatisConfigPath);
        consumerProperties.setParserClassName(getString(consumerConfig, "parser_class_name"));
        consumerProperties.setMappingsPath(getString(consumerConfig, "mappings_path"));
        consumerProperties.setProfilesPath(getString(consumerConfig, "profiles_path"));

        // Load existing queue configurations
        if (consumerConfig.containsKey("queues")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> queueConfigs = (List<Map<String, Object>>) consumerConfig.get("queues");
            List<ConsumerProperties.QueueConfig> queues = new ArrayList<>();
            
            for (Map<String, Object> queueMap : queueConfigs) {
                ConsumerProperties.QueueConfig queueConfig = new ConsumerProperties.QueueConfig();
                queueConfig.setName(getString(queueMap, "name"));
                queueConfig.setEnabled(getBoolean(queueMap, "enabled"));
                queueConfig.setRetryCount(getInteger(queueMap, "retry_count"));
                queueConfig.setRetryDelay(getLong(queueMap, "retry_delay"));
                queueConfig.setPrefetchCount(getInteger(queueMap,"prefetch_count"));
                queueConfig.setBatchSize(getInteger(queueMap, "batch_size"));
                queues.add(queueConfig);
            }
            
            consumerProperties.setQueues(queues);
        }
    }

    // Helper methods for type-safe config reading
    private String getString(Map<String, Object> config, String key) {
        return config.containsKey(key) ? String.valueOf(config.get(key)) : null;
    }

    private Boolean getBoolean(Map<String, Object> config, String key) {
        return config.containsKey(key) ? Boolean.valueOf(String.valueOf(config.get(key))) : false;
    }

    private Integer getInteger(Map<String, Object> config, String key) {
        return config.containsKey(key) ? Integer.valueOf(String.valueOf(config.get(key))) : 0;
    }

    private Long getLong(Map<String, Object> config, String key) {
        return config.containsKey(key) ? Long.valueOf(String.valueOf(config.get(key))) : 0L;
    }

    public String normalizeAndEnsureEndingPathSeparator(String path) {
        String systemPath;
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            systemPath = path.replace('/', '\\');
        } else {
            systemPath = path.replace('\\', '/');
        }
        return systemPath.endsWith(File.separator) ? systemPath : systemPath + File.separator;
    }

    private void validateDirectoryExists(String path, String dirType) throws SFTPSendException {
        File dir = new File(path);
        if (!dir.exists()) {
            if (!dir.mkdirs()) {
                throw new SFTPSendException(dirType + " directory does not exist and could not be created: " + path);
            }
            log.write(LogStream.INFORMATION_MESSAGE, "Created " + dirType + " directory: " + path);
        }
        if (!dir.isDirectory()) {
            throw new SFTPSendException(dirType + " path is not a directory: " + path);
        }
        if (!dir.canRead() || !dir.canWrite()) {
            throw new SFTPSendException(dirType + " directory does not have read/write permissions: " + path);
        }
    }

    // Getters for properties
    public ProducerProperties getProducerProperties() {
        return producerProperties;
    }

    public RabbitMQProperties getRabbitMQProperties() {
        return rabbitMQProperties;
    }

    public ConsumerProperties getConsumerProperties() {
        return consumerProperties;
    }
}
