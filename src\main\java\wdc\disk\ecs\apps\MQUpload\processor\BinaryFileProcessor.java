package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Vector;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

/**
 * Implementation of FileProcessor for handling binary files.
 * This class reads and processes binary files, performing necessary validations
 * and handling binary data appropriately.
 */
public class BinaryFileProcessor implements FileProcessor {
    private LogStream log;
    private DiskProfile dbMsgIDProfile;
    private String dbMsgIDProfileName;
    

    public BinaryFileProcessor(LogStream log, DiskProfile dbMsgIDProfile, String dbMsgIDProfileName) {
        this.log = log;
        this.dbMsgIDProfile = dbMsgIDProfile;
        this.dbMsgIDProfileName = dbMsgIDProfileName;
    }

    @Override
    public List<RabbitMessage> processFile(File file) throws IOException {
        String routingKey = extractRoutingKey(file.getName());
        if (routingKey == null || routingKey.isEmpty()) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Could not determine routing key from file: " + file.getName());
            return Collections.emptyList();
        }

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] content = fis.readAllBytes();
            return Collections.singletonList(new RabbitMessage(
                routingKey,
                content
            ));
        }
    }

    /**
     * Extracts routing key from binary file name.
     * Expected format: TYPE_*.bin or TYPE_*.dat
     * Example: GLDRSLT_20230815.bin -> returns "2" (from pdsMsgId.pro)
     * 
     * @param fileName name of the file to process
     * @return routing key (message ID) from profile, or null if not found
     */
    private String extractRoutingKey(String fileName) {
        String fileType = fileName.split("_")[0];
        try {
            Vector<String> keys = new Vector<>();
            keys.add(DBMsgIDRecord.COMPARE_BY_TYPE);
            keys.add(fileType);

            DBMsgIDRecord dbMsgIDRec = (DBMsgIDRecord) dbMsgIDProfile.getRecord(0, keys);
            if (dbMsgIDRec != null) {
                return dbMsgIDRec.getDBMsgID();
            }
            
            log.write(LogStream.ERROR_MESSAGE, 
                "[Binary Type=" + fileType + "] is not configured in " + dbMsgIDProfileName);
            
        } catch (ProfileException e) {
            log.write(LogStream.ERROR_MESSAGE, 
                "Error getting routing key for binary file: " + e.getMessage());
        }
        return null;
    }
}